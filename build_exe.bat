@echo off
REM Build Windows .exe using the project's virtual environment
call .venv\Scripts\activate
if %errorlevel% neq 0 (
  echo Virtual environment not found. Run scripts\setup_venv.bat first.
  exit /b 1
)

python -m pip install --upgrade pip
python -m pip install pyinstaller

pyinstaller --noconfirm --clean ^
  --onefile ^
  --windowed ^
  --name "AI File Analyzer" ^
  src\app.py

if %errorlevel% neq 0 (
  echo Build failed.
  exit /b %errorlevel%
)

echo.
echo Build complete. EXE is at .\dist\AI File Analyzer.exe

