## AI-Powered File System Analyzer (GUI Edition)

A Windows desktop app that analyzes a WinDirStat-exported CSV using a local AI model and saves an enriched CSV with AI-generated columns.

### Features
- Clean, single-window GUI (CustomTkinter if available; falls back to standard Tkinter)
- Select input CSV and output report via dialogs
- Configurable API URL and max concurrent workers
- Start/Stop controls, progress bar, status updates, and live log
- Multithreaded AI calls via ThreadPoolExecutor
- Writes original columns plus: AI_Category, AI_Importance, AI_Rationale, AI_Action

### Requirements
- Python 3.9+
- A local OpenAI-compatible endpoint (e.g., Ollama OpenAI server) running at `http://localhost:11434/v1/chat/completions`
- Python packages:
  - requests
  - customtkinter (optional; app falls back to Tkinter if not installed)


### Setup with a virtual environment (Windows)
Use the provided scripts to create and use an isolated environment:

```bat
scripts\setup_venv.bat
```

- Creates .venv
- Upgrades pip and installs requirements

Run the app:

```bat
scripts\run_app.bat
```

Build the .exe:

```bat
scripts\build_exe.bat
```

Install packages:

```bash
pip install -r requirements.txt
```

### Run the App (Dev)

```bash
python src/app.py
```

### Using the App
1. Click "Browse..." to choose the WinDirStat-exported CSV.
2. Click "Save As..." to choose where to save the enriched report.
3. Optionally adjust "API URL" and "Max Workers".
4. Click "Start Analysis". The UI will disable inputs, show progress, and stream logs.
5. Click "Stop" to gracefully halt; in-flight requests finish and results are still written.
6. When complete, a dialog appears and the app returns to idle.

Notes:
- The app targets an OpenAI Chat Completions compatible API. It sends a system prompt and each row as a user message, expecting a small JSON object in response.
- The default model name used in code is `llama3.1`. If your server requires a different model, edit `DEFAULT_MODEL` in `src/ai_logic.py`.

### Packaging to a single .exe (Windows)
Build with PyInstaller:

```bash
pip install pyinstaller
./build_windows_exe.bat
```

The executable will be at `./dist/AI File Analyzer.exe`.

### CSV Expectations
- The app reads all columns provided by WinDirStat (or similar) and does not require specific headers.
- It preserves row order in the output.
- On Stop, unprocessed rows get default AI fields and a rationale of "Not processed (stopped early)."

### Troubleshooting
- If the GUI freezes: ensure analysis runs on a separate thread (it does by design). If API is extremely slow, increase timeout by editing `_call_ai` in `src/ai_logic.py`.
- If API calls fail: confirm the endpoint is reachable and supports Chat Completions. Check the Live Log for details.
- Large CSVs: Increase Max Workers carefully; start with 10.

