import os
import queue
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk

# Try to use CustomTkinter if available for a modern look
try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except Exception:
    ctk = None
    CTK_AVAILABLE = False

from ai_logic import analyze_csv

DEFAULT_API_URL = "http://localhost:11434/v1/chat/completions"
DEFAULT_MAX_WORKERS = 10


class App:
    def __init__(self, root: tk.Tk):
        self.root = root
        self.root.title("AI File Analyzer")
        self.root.resizable(False, False)

        if CTK_AVAILABLE:
            ctk.set_appearance_mode("system")
            ctk.set_default_color_theme("blue")

        # Event/Queues
        self.stop_event = threading.Event()
        self.ui_queue = queue.Queue()  # (type, payload)

        # Variables
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.api_url = tk.StringVar(value=DEFAULT_API_URL)
        self.max_workers = tk.StringVar(value=str(DEFAULT_MAX_WORKERS))
        self.status_text = tk.StringVar(value="Idle")

        # Build UI
        self._build_ui()

        # Poll UI queue
        self.root.after(100, self._process_ui_queue)

    # ------------- UI Construction -------------
    def _build_ui(self):
        pad = 10
        outer = self._frame(self.root)
        outer.grid(row=0, column=0, padx=pad, pady=pad, sticky="nsew")

        # File I/O Section
        fio = self._labelframe(outer, text="File I/O")
        fio.grid(row=0, column=0, sticky="ew", padx=pad, pady=(0, pad))
        fio.grid_columnconfigure(1, weight=1)

        # Input
        self._label(fio, text="WinDirStat CSV File:").grid(row=0, column=0, sticky="w", padx=pad, pady=5)
        self.input_entry = self._entry(fio, textvariable=self.input_path, readonly=True)
        self.input_entry.grid(row=0, column=1, sticky="ew", padx=pad, pady=5)
        self._button(fio, text="Browse...", command=self._browse_input).grid(row=0, column=2, padx=pad, pady=5)

        # Output
        self._label(fio, text="Save Report As:").grid(row=1, column=0, sticky="w", padx=pad, pady=5)
        self.output_entry = self._entry(fio, textvariable=self.output_path, readonly=True)
        self.output_entry.grid(row=1, column=1, sticky="ew", padx=pad, pady=5)
        self._button(fio, text="Save As...", command=self._browse_output).grid(row=1, column=2, padx=pad, pady=5)

        # Config Section
        cfg = self._labelframe(outer, text="AI Settings")
        cfg.grid(row=1, column=0, sticky="ew", padx=pad, pady=(0, pad))
        cfg.grid_columnconfigure(1, weight=1)

        self._label(cfg, text="API URL:").grid(row=0, column=0, sticky="w", padx=pad, pady=5)
        self.api_entry = self._entry(cfg, textvariable=self.api_url)
        self.api_entry.grid(row=0, column=1, sticky="ew", padx=pad, pady=5)

        self._label(cfg, text="Max Workers:").grid(row=1, column=0, sticky="w", padx=pad, pady=5)
        self.workers_entry = self._entry(cfg, textvariable=self.max_workers)
        self.workers_entry.grid(row=1, column=1, sticky="w", padx=pad, pady=5)
        if CTK_AVAILABLE:
            # CTkEntry width is in pixels; pick a reasonable width.
            self.workers_entry.configure(width=120)
        else:
            # ttk.Entry width is in characters.
            self.workers_entry.config(width=8)

        # Actions & Progress
        act = self._frame(outer)
        act.grid(row=2, column=0, sticky="ew", padx=pad, pady=(0, pad))
        act.grid_columnconfigure(0, weight=1)

        self.start_btn = self._button(act, text="Start Analysis", command=self._start)
        self.start_btn.grid(row=0, column=0, sticky="w", padx=(0, pad))
        self.stop_btn = self._button(act, text="Stop", command=self._stop, state="disabled")
        self.stop_btn.grid(row=0, column=1, sticky="w")

        # Progress bar
        if CTK_AVAILABLE:
            self.progress = ctk.CTkProgressBar(act)
            self.progress.set(0)
            self.progress.grid(row=0, column=2, padx=(pad, 0), pady=5, sticky="ew")
            act.grid_columnconfigure(2, weight=1)
        else:
            self.progress = ttk.Progressbar(act, orient="horizontal", mode="determinate", length=300)
            self.progress.grid(row=0, column=2, padx=(pad, 0), pady=5, sticky="ew")
            act.grid_columnconfigure(2, weight=1)

        # Status label
        self.status_lbl = self._label(outer, textvariable=self.status_text)
        self.status_lbl.grid(row=3, column=0, sticky="w", padx=pad)

        # Live Log
        logf = self._labelframe(outer, text="Live Log")
        logf.grid(row=4, column=0, sticky="nsew", padx=pad, pady=(0, pad))
        self.log_txt = scrolledtext.ScrolledText(logf, width=100, height=18, state="disabled", wrap=tk.WORD)
        self.log_txt.pack(fill="both", expand=True, padx=pad, pady=pad)

        self._update_start_enabled()

    # ------------- Helpers to abstract CTk/Tk -------------
    def _frame(self, parent, **kwargs):
        if CTK_AVAILABLE:
            return ctk.CTkFrame(parent, **kwargs)
        return ttk.Frame(parent, **kwargs)

    def _labelframe(self, parent, text: str):
        if CTK_AVAILABLE:
            # Wrapper frame with a title label and an inner content frame.
            # Use only 'grid' inside the wrapper to avoid mixing geometry managers.
            frm = ctk.CTkFrame(parent)
            frm.grid_columnconfigure(0, weight=1)

            lbl = ctk.CTkLabel(frm, text=text, font=("", 14, "bold"))
            lbl.grid(row=0, column=0, sticky="w", padx=6, pady=(6, 0))

            inner = ctk.CTkFrame(frm)
            inner.grid(row=1, column=0, sticky="ew", padx=6, pady=6)

            # Proxy geometry management so callers can call grid(...) on the returned
            # content frame but it will actually place the wrapper in the parent.
            inner.grid = frm.grid  # type: ignore[attr-defined]
            return inner
        return ttk.LabelFrame(parent, text=text)

    def _label(self, parent, **kwargs):
        if CTK_AVAILABLE:
            return ctk.CTkLabel(parent, **kwargs)
        return ttk.Label(parent, **kwargs)

    def _entry(self, parent, textvariable=None, readonly: bool = False):
        if CTK_AVAILABLE:
            ent = ctk.CTkEntry(parent, textvariable=textvariable)
            if readonly:
                ent.configure(state="readonly")
            return ent
        ent = ttk.Entry(parent, textvariable=textvariable)
        if readonly:
            ent.state(["readonly"])  # type: ignore
        return ent

    def _button(self, parent, **kwargs):
        if CTK_AVAILABLE:
            return ctk.CTkButton(parent, **kwargs)
        return ttk.Button(parent, **kwargs)

    # ------------- UI Actions -------------
    def _browse_input(self):
        path = filedialog.askopenfilename(
            title="Select WinDirStat CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
        )
        if path:
            self.input_path.set(path)
            self._update_start_enabled()

    def _browse_output(self):
        path = filedialog.asksaveasfilename(
            title="Save Enriched Report",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
        )
        if path:
            self.output_path.set(path)
            self._update_start_enabled()

    def _update_start_enabled(self):
        enabled = bool(self.input_path.get() and self.output_path.get())
        self._set_button_state(self.start_btn, "normal" if enabled else "disabled")

    def _set_button_state(self, btn, state: str):
        try:
            btn.configure(state=state)
        except Exception:
            btn.state([state])  # ttk fallback

    def _set_entry_state(self, ent, readonly: bool):
        try:
            ent.configure(state="readonly" if readonly else "normal")
        except Exception:
            if readonly:
                ent.state(["readonly"])  # type: ignore
            else:
                ent.state(["!readonly"])  # type: ignore

    # ------------- Logging & UI queue -------------
    def _log(self, text: str):
        self.log_txt.configure(state="normal")
        self.log_txt.insert(tk.END, text + "\n")
        self.log_txt.see(tk.END)
        self.log_txt.configure(state="disabled")

    def _enqueue(self, etype: str, payload):
        self.ui_queue.put((etype, payload))

    def _process_ui_queue(self):
        try:
            while True:
                etype, payload = self.ui_queue.get_nowait()
                if etype == "log":
                    self._log(payload)
                elif etype == "status":
                    self.status_text.set(payload)
                elif etype == "progress":
                    done, total = payload
                    if CTK_AVAILABLE:
                        self.progress.set(0 if total == 0 else done / max(1, total))
                    else:
                        self.progress["maximum"] = max(1, total)
                        self.progress["value"] = done
                elif etype == "finished":
                    success = payload
                    if success:
                        messagebox.showinfo("AI File Analyzer", "Analysis Complete!")
                    else:
                        messagebox.showinfo("AI File Analyzer", "Analysis Stopped.")
                    self._set_idle_state()
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self._process_ui_queue)

    # ------------- Start/Stop flow -------------
    def _start(self):
        try:
            api = self.api_url.get().strip()
            if not api:
                messagebox.showerror("Error", "API URL is required.")
                return
            try:
                workers = int(self.max_workers.get())
            except Exception:
                messagebox.showerror("Error", "Max Workers must be an integer.")
                return
            inp = self.input_path.get()
            outp = self.output_path.get()
            if not os.path.isfile(inp):
                messagebox.showerror("Error", "Input file does not exist.")
                return
            self._set_busy_state()

            # Clear progress and status
            if CTK_AVAILABLE:
                self.progress.set(0)
            else:
                self.progress["maximum"] = 1
                self.progress["value"] = 0
            self.status_text.set("Starting analysis...")
            self._enqueue("log", "Starting analysis...")

            self.stop_event.clear()

            def progress_cb(done, total):
                self._enqueue("progress", (done, total))

            def status_cb(text):
                self._enqueue("status", text)

            def log_cb(text):
                self._enqueue("log", text)

            def worker():
                try:
                    analyze_csv(
                        input_csv_path=inp,
                        output_csv_path=outp,
                        api_url=api,
                        max_workers=workers,
                        stop_event=self.stop_event,
                        progress=progress_cb,
                        status=status_cb,
                        log=log_cb,
                    )
                    self._enqueue("finished", not self.stop_event.is_set())
                except Exception as e:
                    self._enqueue("log", f"Fatal error: {e}")
                    self._enqueue("status", "Error encountered. See log.")
                    self._enqueue("finished", False)

            threading.Thread(target=worker, daemon=True).start()
        except Exception as e:
            self._enqueue("log", f"Unhandled error: {e}")
            self._set_idle_state()

    def _stop(self):
        self._enqueue("log", "Stop requested. Finishing in-flight tasks...")
        self.stop_event.set()
        self._set_button_state(self.stop_btn, "disabled")

    # ------------- State toggles -------------
    def _set_busy_state(self):
        self._set_button_state(self.start_btn, "disabled")
        self._set_button_state(self.stop_btn, "normal")
        self._set_entry_state(self.input_entry, True)
        self._set_entry_state(self.output_entry, True)
        self._set_entry_state(self.api_entry, True)
        self._set_entry_state(self.workers_entry, True)

    def _set_idle_state(self):
        self._set_button_state(self.stop_btn, "disabled")
        self._set_entry_state(self.input_entry, True)
        self._set_entry_state(self.output_entry, True)
        self._set_entry_state(self.api_entry, False)
        self._set_entry_state(self.workers_entry, False)
        self._update_start_enabled()
        self.status_text.set("Idle")


def main():
    root = ctk.CTk() if CTK_AVAILABLE else tk.Tk()
    App(root)
    root.mainloop()


if __name__ == "__main__":
    main()

