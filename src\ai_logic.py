import csv
import json
import re
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Any, Callable, Dict, List, Optional

import ollama

# Callback types
LogCallback = Callable[[str], None]
StatusCallback = Callable[[str], None]
ProgressCallback = Callable[[int, int], None]

DEFAULT_MODEL = "llama3.1"
SYSTEM_PROMPT = (
    "You are a professional File System Analyst. Given a CSV row representing a file or folder, "
    "analyze its likely category, importance, rationale, and a recommended action. "
    "Return ONLY a compact JSON object with these exact keys: "
    "AI_Category (one of: System, Application, Media, Document, Archive, Temporary, Log, Backup, Unknown), "
    "AI_Importance (integer 1-10), AI_Rationale (short sentence), AI_Action (one of: Keep, Review, Archive, Delete)."
)


def _extract_json(text: str) -> Optional[Dict[str, Any]]:
    """Try to parse a JSON object from response text. Returns dict or None."""
    # Fast path
    try:
        return json.loads(text)
    except Exception:
        pass
    # Fallback: extract first {...} block
    m = re.search(r"\{.*\}", text, flags=re.DOTALL)
    if not m:
        return None
    try:
        return json.loads(m.group(0))
    except Exception:
        return None


def _normalize_ollama_host(api_url: str) -> str:
    """Return an Ollama host URL (e.g., http://localhost:11434) from various inputs."""
    base = (api_url or "").strip().rstrip("/")
    for suffix in ("/v1/chat/completions", "/v1/chat", "/api/chat", "/api/generate"):
        if base.endswith(suffix):
            return base[: -len(suffix)]
    return base


def _call_ai(api_url: str, row_data: Dict[str, Any], timeout: int = 60, log: Optional[LogCallback] = None) -> Dict[str, Any]:
    """Call Ollama via its Python package and return parsed JSON fields.

    Returns a dict with keys: AI_Category, AI_Importance, AI_Rationale, AI_Action
    On failure, returns defaults with rationale containing the error.
    """
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": json.dumps(row_data, ensure_ascii=False)},
    ]
    try:
        host = _normalize_ollama_host(api_url)
        client = ollama.Client(host=host) if host else ollama.Client()
        data = client.chat(model=DEFAULT_MODEL, messages=messages, options={"temperature": 0.2})
        content = data.get("message", {}).get("content", "")
        parsed = _extract_json(content) or {}
        return {
            "AI_Category": str(parsed.get("AI_Category", "Unknown"))[:32],
            "AI_Importance": int(parsed.get("AI_Importance", 5)),
            "AI_Rationale": str(parsed.get("AI_Rationale", "No rationale provided."))[:512],
            "AI_Action": str(parsed.get("AI_Action", "Review"))[:32],
        }
    except Exception as e:
        if log:
            log(f"AI call failed: {e}")
        return {
            "AI_Category": "Unknown",
            "AI_Importance": 5,
            "AI_Rationale": f"AI error: {e}",
            "AI_Action": "Review",
        }


def analyze_csv(
    input_csv_path: str,
    output_csv_path: str,
    api_url: str,
    max_workers: int,
    stop_event: threading.Event,
    progress: ProgressCallback,
    status: StatusCallback,
    log: LogCallback,
):
    """Analyze a WinDirStat-exported CSV and write an enriched CSV (streaming).

    - Streams input CSV instead of loading all rows into memory (handles very large files)
    - Calls AI concurrently using ThreadPoolExecutor with a bounded in-flight window
    - Preserves original row order when writing by buffering only a small window of rows
    - Periodically reports progress and status via callbacks
    - On Stop: finishes in-flight tasks, then writes defaults for remaining rows
    """

    # Determine total rows (for progress). Fallback to 0 if counting fails.
    total_rows = 0
    try:
        status("Counting rows for progress...")
        with open(input_csv_path, "r", newline="", encoding="utf-8-sig") as fcount:
            # Count lines; subtract header line.
            total_rows = max(0, sum(1 for _ in fcount) - 1)
    except Exception as e:
        log(f"Row count failed: {e}. Proceeding without total.")
        total_rows = 0

    log(f"Using API: {api_url} | Model: {DEFAULT_MODEL} | Workers: {max_workers}")

    # Limit workers minimally to [1, 64]
    max_workers = max(1, min(int(max_workers or 1), 64))
    window = max_workers * 2  # bounded in-flight tasks and rows buffer size

    # Defaults for AI fields
    def _default_ai(reason: str = "Not processed (stopped early).") -> Dict[str, Any]:
        return {
            "AI_Category": "Unknown",
            "AI_Importance": 5,
            "AI_Rationale": reason,
            "AI_Action": "Review",
        }

    with open(input_csv_path, "r", newline="", encoding="utf-8-sig") as fin, \
         open(output_csv_path, "w", newline="", encoding="utf-8") as fout:
        reader = csv.DictReader(fin)
        fieldnames = reader.fieldnames or []
        out_fields = fieldnames + ["AI_Category", "AI_Importance", "AI_Rationale", "AI_Action"]
        writer = csv.DictWriter(fout, fieldnames=out_fields)
        writer.writeheader()

        # State
        next_to_submit = 0
        next_to_write = 0
        completed = 0
        no_more_rows = False
        rows_buffer: Dict[int, Dict[str, Any]] = {}
        results: Dict[int, Dict[str, Any]] = {}

        def submit_task(idx: int, row: Dict[str, Any]):
            payload = dict(row)
            return idx, _call_ai(api_url, payload, log=log)

        status(f"Processing 0 of {total_rows or '?'}...")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures: Dict[Any, int] = {}

            def try_submit_more():
                nonlocal next_to_submit, no_more_rows
                while (not stop_event.is_set()) and (len(futures) < window) and (not no_more_rows):
                    try:
                        row = next(reader)
                    except StopIteration:
                        no_more_rows = True
                        break
                    rows_buffer[next_to_submit] = row
                    fut = executor.submit(submit_task, next_to_submit, row)
                    futures[fut] = next_to_submit
                    next_to_submit += 1

            # Prime the pipeline
            try_submit_more()

            while futures:
                for fut in as_completed(list(futures.keys())):
                    idx = futures.pop(fut)
                    if stop_event.is_set():
                        # Still collect results for already submitted tasks.
                        pass
                    try:
                        i, res = fut.result()
                        results[i] = res
                    except Exception as e:
                        log(f"Worker failed for row {idx}: {e}")
                        results[idx] = _default_ai(reason=f"Worker error: {e}")

                    # Write in order as far as possible
                    while next_to_write in results:
                        row = rows_buffer.pop(next_to_write, {})
                        ai = results.pop(next_to_write)
                        out_row = dict(row)
                        out_row.update(ai)
                        writer.writerow(out_row)
                        completed += 1
                        progress(completed, total_rows)
                        if completed % 25 == 0 or (total_rows and completed == total_rows):
                            status(f"Processing {completed} of {total_rows or '?'}...")

                    # Keep pipeline filled
                    try_submit_more()

            # If stopped, write defaults for any remaining rows not submitted yet
            if stop_event.is_set():
                # First, flush any buffered rows that were submitted but whose results
                # did not arrive (should be none because we waited for 'futures' to empty)
                # Then, write defaults for the remainder of the file.
                if not no_more_rows:
                    for row in reader:
                        out_row = dict(row)
                        out_row.update(_default_ai())
                        writer.writerow(out_row)
                        completed += 1
                        progress(completed, total_rows)
                        if completed % 25 == 0:
                            status(f"Processing {completed} of {total_rows or '?'}...")

        if stop_event.is_set():
            status("Stopped by user.")
            log(f"Stopped: wrote {completed} rows (some defaulted).")
        else:
            status("Analysis complete!")
            log("All rows processed and output written.")
