@echo off
setlocal

REM Create and prepare a local virtual environment in .venv

if not exist .venv (
  echo Creating virtual environment in .venv ...
  py -3 -m venv .venv
  if %errorlevel% neq 0 (
    echo Failed to create virtual environment.
    exit /b %errorlevel%
  )
) else (
  echo Virtual environment already exists at .venv
)

call .venv\Scripts\activate
if %errorlevel% neq 0 (
  echo Failed to activate virtual environment.
  exit /b %errorlevel%
)

echo Upgrading pip and installing requirements...
python -m pip install --upgrade pip
pip install -r requirements.txt
if %errorlevel% neq 0 (
  echo Failed to install requirements.
  exit /b %errorlevel%
)

echo.
echo Virtual environment ready.
echo - To run the app: scripts\run_app.bat
echo - To build the exe: scripts\build_exe.bat
endlocal

